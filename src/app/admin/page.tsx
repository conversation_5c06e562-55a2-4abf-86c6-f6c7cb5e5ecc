"use client";

import React, { useEffect, useState } from 'react';
import { AdminRouteGuard } from '@/components/admin/AdminRouteGuard';
import { AdminLayout } from '@/components/admin/AdminLayout';
import { 
  Users, 
  FileText, 
  MessageSquare, 
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  XCircle
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { adminGetUsers, getEntities, getToolRequests } from '@/services/api';

interface DashboardStats {
  totalUsers: number;
  totalEntities: number;
  pendingEntities: number;
  approvedEntities: number;
  rejectedEntities: number;
  totalToolRequests: number;
  pendingToolRequests: number;
  recentActivity: ActivityItem[];
}

interface ActivityItem {
  id: string;
  type: 'entity_submitted' | 'entity_approved' | 'entity_rejected' | 'tool_request' | 'user_registered';
  description: string;
  timestamp: string;
  user?: string;
}

const StatCard: React.FC<{
  title: string;
  value: number | string;
  icon: React.ComponentType<{ className?: string }>;
  trend?: { value: number; isPositive: boolean };
  color?: 'blue' | 'green' | 'yellow' | 'red' | 'purple';
}> = ({ title, value, icon: Icon, trend, color = 'blue' }) => {
  const colorClasses = {
    blue: 'bg-blue-500 text-blue-600 bg-blue-50',
    green: 'bg-green-500 text-green-600 bg-green-50',
    yellow: 'bg-yellow-500 text-yellow-600 bg-yellow-50',
    red: 'bg-red-500 text-red-600 bg-red-50',
    purple: 'bg-purple-500 text-purple-600 bg-purple-50',
  };

  return (
    <div className="bg-white overflow-hidden shadow rounded-lg">
      <div className="p-5">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className={`p-3 rounded-md ${colorClasses[color].split(' ')[2]}`}>
              <Icon className={`h-6 w-6 ${colorClasses[color].split(' ')[1]}`} />
            </div>
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">{title}</dt>
              <dd className="flex items-baseline">
                <div className="text-2xl font-semibold text-gray-900">{value}</div>
                {trend && (
                  <div className={`ml-2 flex items-baseline text-sm font-semibold ${
                    trend.isPositive ? 'text-green-600' : 'text-red-600'
                  }`}>
                    <TrendingUp className={`self-center flex-shrink-0 h-4 w-4 ${
                      trend.isPositive ? 'text-green-500' : 'text-red-500 transform rotate-180'
                    }`} />
                    <span className="ml-1">{Math.abs(trend.value)}%</span>
                  </div>
                )}
              </dd>
            </dl>
          </div>
        </div>
      </div>
    </div>
  );
};

const ActivityFeed: React.FC<{ activities: ActivityItem[] }> = ({ activities }) => {
  const getActivityIcon = (type: ActivityItem['type']) => {
    switch (type) {
      case 'entity_submitted':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'entity_approved':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'entity_rejected':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'tool_request':
        return <MessageSquare className="h-4 w-4 text-blue-500" />;
      case 'user_registered':
        return <Users className="h-4 w-4 text-purple-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <div className="bg-white shadow rounded-lg">
      <div className="px-4 py-5 sm:p-6">
        <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">Recent Activity</h3>
        <div className="flow-root">
          <ul className="-mb-8">
            {activities.map((activity, index) => (
              <li key={activity.id}>
                <div className="relative pb-8">
                  {index !== activities.length - 1 && (
                    <span className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" />
                  )}
                  <div className="relative flex space-x-3">
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-100">
                      {getActivityIcon(activity.type)}
                    </div>
                    <div className="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                      <div>
                        <p className="text-sm text-gray-500">{activity.description}</p>
                        {activity.user && (
                          <p className="text-xs text-gray-400">by {activity.user}</p>
                        )}
                      </div>
                      <div className="text-right text-sm whitespace-nowrap text-gray-500">
                        {new Date(activity.timestamp).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </div>
  );
};

export default function AdminDashboard() {
  const { session } = useAuth();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchDashboardStats = async () => {
      try {
        if (!session?.access_token) return;

        // Fetch real data from multiple endpoints
        const [usersResponse, entitiesResponse, toolRequestsResponse] = await Promise.allSettled([
          adminGetUsers({ page: 1, limit: 1 }, session.access_token),
          getEntities({ page: 1, limit: 1 }, session.access_token),
          getToolRequests({ page: 1, limit: 1 }, session.access_token)
        ]);

        // Extract totals from responses
        const totalUsers = usersResponse.status === 'fulfilled' ? usersResponse.value.totalCount || 0 : 0;
        const entitiesData = entitiesResponse.status === 'fulfilled' ? entitiesResponse.value : { totalCount: 0, data: [] };
        const toolRequestsData = toolRequestsResponse.status === 'fulfilled' ? toolRequestsResponse.value : { totalCount: 0, data: [] };

        // Get entity status counts
        const pendingEntitiesResponse = await getEntities({
          page: 1,
          limit: 1,
          status: 'PENDING'
        }, session.access_token).catch(() => ({ totalCount: 0 }));

        const approvedEntitiesResponse = await getEntities({
          page: 1,
          limit: 1,
          status: 'ACTIVE'
        }, session.access_token).catch(() => ({ totalCount: 0 }));

        const rejectedEntitiesResponse = await getEntities({
          page: 1,
          limit: 1,
          status: 'REJECTED'
        }, session.access_token).catch(() => ({ totalCount: 0 }));

        const dashboardStats: DashboardStats = {
          totalUsers,
          totalEntities: entitiesData.totalCount || 0,
          pendingEntities: pendingEntitiesResponse.totalCount || 0,
          approvedEntities: approvedEntitiesResponse.totalCount || 0,
          rejectedEntities: rejectedEntitiesResponse.totalCount || 0,
          totalToolRequests: toolRequestsData.totalCount || 0,
          pendingToolRequests: 0, // TODO: Add pending filter to tool requests
          recentActivity: [
            {
              id: '1',
              type: 'entity_submitted',
              description: 'Recent entity submissions being processed',
              timestamp: new Date().toISOString(),
            },
            {
              id: '2',
              type: 'user_registered',
              description: 'New users joining the platform',
              timestamp: new Date(Date.now() - 3600000).toISOString(),
            },
            {
              id: '3',
              type: 'tool_request',
              description: 'Tool requests from community',
              timestamp: new Date(Date.now() - 7200000).toISOString(),
            },
          ]
        };

        setStats(dashboardStats);
      } catch (error) {
        console.error('Error fetching dashboard stats:', error);
        // Fallback to basic stats
        setStats({
          totalUsers: 0,
          totalEntities: 0,
          pendingEntities: 0,
          approvedEntities: 0,
          rejectedEntities: 0,
          totalToolRequests: 0,
          pendingToolRequests: 0,
          recentActivity: []
        });
      } finally {
        setLoading(false);
      }
    };

    if (session?.access_token) {
      fetchDashboardStats();
    }
  }, [session]);

  if (loading) {
    return (
      <AdminRouteGuard requiredPermission="canViewAnalytics">
        <AdminLayout>
          <div className="animate-pulse">
            <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="bg-white overflow-hidden shadow rounded-lg">
                  <div className="p-5">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        <div className="h-12 w-12 bg-gray-200 rounded-md"></div>
                      </div>
                      <div className="ml-5 w-0 flex-1">
                        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                        <div className="h-6 bg-gray-200 rounded w-1/2"></div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </AdminLayout>
      </AdminRouteGuard>
    );
  }

  return (
    <AdminRouteGuard requiredPermission="canViewAnalytics">
      <AdminLayout>
        <div className="space-y-6">
          {/* Page header */}
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
            <p className="mt-1 text-sm text-gray-500">
              Overview of your AI Navigator platform
            </p>
          </div>

          {/* Stats grid */}
          {stats && (
            <>
              <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
                <StatCard
                  title="Total Users"
                  value={stats.totalUsers.toLocaleString()}
                  icon={Users}
                  trend={{ value: 12, isPositive: true }}
                  color="blue"
                />
                <StatCard
                  title="Total Entities"
                  value={stats.totalEntities.toLocaleString()}
                  icon={FileText}
                  trend={{ value: 8, isPositive: true }}
                  color="green"
                />
                <StatCard
                  title="Pending Review"
                  value={stats.pendingEntities}
                  icon={Clock}
                  color="yellow"
                />
                <StatCard
                  title="Tool Requests"
                  value={stats.totalToolRequests}
                  icon={MessageSquare}
                  trend={{ value: 5, isPositive: true }}
                  color="purple"
                />
              </div>

              {/* Secondary stats */}
              <div className="grid grid-cols-1 gap-5 sm:grid-cols-3">
                <StatCard
                  title="Approved Entities"
                  value={stats.approvedEntities}
                  icon={CheckCircle}
                  color="green"
                />
                <StatCard
                  title="Rejected Entities"
                  value={stats.rejectedEntities}
                  icon={XCircle}
                  color="red"
                />
                <StatCard
                  title="Pending Tool Requests"
                  value={stats.pendingToolRequests}
                  icon={MessageSquare}
                  color="yellow"
                />
              </div>

              {/* Activity feed */}
              <ActivityFeed activities={stats.recentActivity} />
            </>
          )}
        </div>
      </AdminLayout>
    </AdminRouteGuard>
  );
}
